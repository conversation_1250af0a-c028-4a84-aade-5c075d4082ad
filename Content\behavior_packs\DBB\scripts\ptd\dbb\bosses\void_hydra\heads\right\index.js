import { selectAttack } from "./controller";
import { getTarget } from "../../../general_mechanics/targetUtils";
// Import all attack functions
import { executeRightAtomicCrossAttack } from "./attacks/rightAtomicCross";
import { executeRightHeadAtomicAttack } from "./attacks/rightAtomic";
import { executeRightVacuumAttack } from "./attacks/rightVacuum";
import { executeRightSummonAttack } from "./attacks/rightSummon";
/**
 * Handles the void hydra boss mechanics using event-driven system
 * Called when the environment sensor triggers an attack
 *
 * @param rightHead The void hydra right head entity
 */
export function voidHydraRightHeadMechanics(rightHead) {
    try {
        // Skip if entity is not valid
        if (!rightHead)
            return;
        // Skip if entity is spawning or dead
        const isSpawning = rightHead.getProperty("ptd_dbb:spawning");
        const isDead = rightHead.getProperty("ptd_dbb:dead");
        if (isSpawning || isDead)
            return;
        // Handle attack mechanics using event-driven system
        const attack = rightHead.getProperty("ptd_dbb:attack");
        const coolingDown = rightHead.getProperty("ptd_dbb:cooling_down");
        if (attack && attack !== "none") {
            // Attack is active - execution is handled by individual attack functions using runTimeout
            // Execute attack-specific logic once when attack starts
            executeAttackLogic(rightHead, attack);
            return;
        }
        else if (!coolingDown) {
            // No attack is active and not cooling down, handle attack selection
            const target = getTarget(rightHead, rightHead.location, 32, ["void_hydra"]);
            if (target) {
                selectAttack(rightHead, target);
                // After selecting attack, immediately execute it if one was selected
                const selectedAttackType = rightHead.getProperty("ptd_dbb:attack");
                if (selectedAttackType && selectedAttackType !== "none") {
                    executeAttackLogic(rightHead, selectedAttackType);
                }
            }
        }
    }
    catch (error) {
        console.warn(`Error in void hydra mechanics: ${error}`);
    }
}
/**
 * Executes attack-specific logic based on the current attack and timer
 * @param rightHead The void hydra right head entity
 * @param attack The current attack type
 * @param attackTimer The current attack timer
 */
function executeAttackLogic(rightHead, attack) {
    console.warn(`Executing attack ${attack}`);
    try {
        switch (attack) {
            case "right_atomic_cross":
                executeRightAtomicCrossAttack(rightHead);
                break;
            case "right_atomic":
                executeRightHeadAtomicAttack(rightHead);
                break;
            case "right_vacuum":
                executeRightVacuumAttack(rightHead);
                break;
            case "right_summon":
                executeRightSummonAttack(rightHead);
                break;
            default:
                // Unknown attack type
                break;
        }
    }
    catch (error) {
        console.warn(`Error executing attack ${attack}: ${error}`);
    }
}
